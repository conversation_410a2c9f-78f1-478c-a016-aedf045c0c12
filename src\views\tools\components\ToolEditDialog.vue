<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑工具' : '添加工具'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      label-position="left"
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="工具名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入工具名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本号" prop="version">
            <el-input v-model="formData.version" placeholder="请输入版本号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="类别" prop="category">
            <el-select v-model="formData.category" placeholder="请选择类别" style="width: 100%">
              <el-option
                v-for="item in categoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择类型" style="width: 100%">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="提供者" prop="provider">
            <el-input v-model="formData.provider" placeholder="请输入提供者" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="已上架" value="online" />
              <el-option label="已下架" value="offline" />
              <el-option label="维护中" value="maintenance" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="工具图标">
        <div class="icon-upload">
          <el-upload
            class="avatar-uploader"
            :action="uploadAction"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
          >
            <img v-if="formData.icon" :src="formData.icon" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tips">
            <p>点击上传工具图标</p>
            <p class="text-xs text-gray-500">支持 JPG、PNG 格式，大小不超过 2MB</p>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="工具描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入工具描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from 'vue';
  import { ElMessage } from 'element-plus';
  import { Plus } from '@element-plus/icons-vue';
  import type { FormInstance, FormRules, UploadProps } from 'element-plus';
  import type { Tool, ToolDTO } from '@/api/modules/tools';
  import {
    createTool,
    updateTool,
    uploadToolIcon,
    getToolCategories,
    getToolTypes,
  } from '@/api/modules/tools';

  interface Props {
    modelValue: boolean;
    tool?: Tool | null;
  }

  interface Emits {
    (e: 'update:modelValue', value: boolean): void;
    (e: 'success'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  // 响应式数据
  const formRef = ref<FormInstance>();
  const submitLoading = ref(false);
  const uploadAction = ref('/api/tools/upload-icon'); // 上传接口地址

  // 表单数据
  const formData = reactive<ToolDTO>({
    name: '',
    description: '',
    category: '',
    type: '',
    provider: '',
    version: '',
    icon: '',
    status: 'offline',
  });

  // 选项数据
  const categoryOptions = ref<{ label: string; value: string }[]>([]);
  const typeOptions = ref<{ label: string; value: string }[]>([]);

  // 表单验证规则
  const rules: FormRules = {
    name: [
      { required: true, message: '请输入工具名称', trigger: 'blur' },
      { min: 2, max: 50, message: '工具名称长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    category: [
      { required: true, message: '请选择工具类别', trigger: 'change' },
    ],
    type: [
      { required: true, message: '请选择工具类型', trigger: 'change' },
    ],
    provider: [
      { required: true, message: '请输入提供者', trigger: 'blur' },
    ],
    version: [
      { required: true, message: '请输入版本号', trigger: 'blur' },
      { pattern: /^v?\d+\.\d+(\.\d+)?.*$/, message: '版本号格式不正确', trigger: 'blur' },
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'change' },
    ],
  };

  // 计算属性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  const isEdit = computed(() => !!props.tool?.id);

  // 方法
  const fetchOptions = async () => {
    try {
      const [categoriesRes, typesRes] = await Promise.all([
        getToolCategories(),
        getToolTypes(),
      ]);
      
      if (categoriesRes.code === 0) {
        categoryOptions.value = categoriesRes.data.map(item => ({
          label: item,
          value: item,
        }));
      }
      
      if (typesRes.code === 0) {
        typeOptions.value = typesRes.data.map(item => ({
          label: item,
          value: item,
        }));
      }
    } catch (error) {
      console.error('获取选项数据失败:', error);
    }
  };

  const resetForm = () => {
    Object.assign(formData, {
      name: '',
      description: '',
      category: '',
      type: '',
      provider: '',
      version: '',
      icon: '',
      status: 'offline',
    });
    formRef.value?.clearValidate();
  };

  const initFormData = () => {
    if (props.tool) {
      Object.assign(formData, {
        id: props.tool.id,
        name: props.tool.name,
        description: props.tool.description || '',
        category: props.tool.category,
        type: props.tool.type,
        provider: props.tool.provider,
        version: props.tool.version,
        icon: props.tool.icon || '',
        status: props.tool.status,
      });
    } else {
      resetForm();
    }
  };

  const handleSubmit = async () => {
    if (!formRef.value) return;
    
    try {
      const valid = await formRef.value.validate();
      if (!valid) return;

      submitLoading.value = true;
      
      let response;
      if (isEdit.value) {
        response = await updateTool(formData.id!, formData);
      } else {
        response = await createTool(formData);
      }
      
      if (response.code === 0) {
        ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
        emit('success');
        handleClose();
      }
    } catch (error) {
      console.error('提交失败:', error);
      ElMessage.error('操作失败');
    } finally {
      submitLoading.value = false;
    }
  };

  const handleClose = () => {
    dialogVisible.value = false;
    nextTick(() => {
      resetForm();
    });
  };

  // 图片上传相关
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    const isImage = file.type.startsWith('image/');
    const isLt2M = file.size / 1024 / 1024 < 2;

    if (!isImage) {
      ElMessage.error('只能上传图片文件!');
      return false;
    }
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过 2MB!');
      return false;
    }
    return true;
  };

  const handleUploadSuccess = (response: any) => {
    if (response.code === 0) {
      formData.icon = response.data.url;
      ElMessage.success('图标上传成功');
    } else {
      ElMessage.error('图标上传失败');
    }
  };

  const handleUploadError = () => {
    ElMessage.error('图标上传失败');
  };

  // 监听对话框显示状态
  watch(
    () => props.modelValue,
    (visible) => {
      if (visible) {
        fetchOptions();
        nextTick(() => {
          initFormData();
        });
      }
    }
  );
</script>

<style lang="scss" scoped>
  .icon-upload {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .avatar-uploader {
      .avatar {
        width: 80px;
        height: 80px;
        border-radius: 6px;
        object-fit: cover;
      }
    }

    .upload-tips {
      flex: 1;
      
      p {
        margin: 0;
        line-height: 1.5;
        
        &:first-child {
          font-weight: 500;
        }
      }
    }
  }

  :deep(.avatar-uploader .el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.avatar-uploader .el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  :deep(.avatar-uploader-icon) {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }
</style>
