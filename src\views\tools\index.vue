<template>
  <div class="h-full">
    <div v-loading="loading" class="project-list">
      <div class="header flex items-center px-5">
        <h2 class="mr-5 text-2xl">工具</h2>

        <el-menu class="relative bottom--1px" mode="horizontal" :ellipsis="false" default-active="1" router>
          <el-menu-item index="1"> 所有工具 </el-menu-item>
        </el-menu>
      </div>

      <div class="flex gap-4 px-5 py-3">
        <div>
          <el-input v-model="filters.name" placeholder="请输入名称" clearable></el-input>
        </div>

        <div>
          <el-select v-model="filters.category" placeholder="所有类别" clearable>
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>

        <div>
          <el-select v-model="filters.type" placeholder="所有类型" clearable>
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>

      <div class="h-0 flex flex-1 flex-col px-5 pb-2">
        <el-table
          ref="multipleTableRef"
          class="c-table-header h-0 flex-1"
          :data="tableData"
          style="width: 100%"
          highlight-current-row
          @row-click="handleRowClick"
        >
          <el-table-column prop="name" label="名称" min-width="100px" />
          <el-table-column prop="category" label="类别" />
          <el-table-column prop="type" label="类型" />
          <el-table-column prop="provider" label="提供者" />
          <el-table-column prop="version" label="版本号" />
        </el-table>

        <div class="pagination-container mt-4 flex justify-center">
          <el-pagination
            background
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, watch, onBeforeMount } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  // 1. 类型定义和接口
  interface Tools {
    id: number;
    name: string;
    category: string;
    type: string;
    provider: string;
    version: string;
  }

  // 2. 响应式数据
  const loading = ref(false);
  const tableData = ref<Tools[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(30);

  const filters = reactive({
    name: '',
    category: '',
    type: '',
  });

  const categoryOptions = [
    { label: '所有类别', value: '' },
    { label: 'Mapping Manipuation', value: 'mapping-manipulation' },
    { label: 'Read Mapping', value: 'read-mapping' },
    { label: 'RNA-Seq', value: 'rna-seq' },
  ];

  const typeOptions = [
    { label: '所有类型', value: '' },
    { label: 'APP', value: 'app' },
  ];

  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    fetchData();
  };

  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchData();
  };

  // 处理行点击事件
  const handleRowClick = (row: Tools) => {
    router.push({
      path: '/tools/tools-detail',
      query: { id: row.id },
    });
  };

  async function fetchData() {
    try {
      loading.value = true;
      // const { data } = await dataBrowse();
      await new Promise((resolve) => setTimeout(resolve, 300));
      tableData.value = [
        {
          id: 1,
          name: 'BWA',
          category: 'Read Mapping',
          type: 'APP',
          provider: '系统',
          version: 'v0.7.17',
        },
        {
          id: 2,
          name: 'STAR',
          category: 'RNA-Seq',
          type: 'APP',
          provider: '系统',
          version: 'v2.7.10a',
        },
        {
          id: 3,
          name: 'Samtools',
          category: 'Mapping Manipuation',
          type: 'APP',
          provider: '系统',
          version: 'v1.15',
        },
        {
          id: 4,
          name: 'HISAT2',
          category: 'RNA-Seq',
          type: 'APP',
          provider: '系统',
          version: 'v2.2.1',
        },
        {
          id: 5,
          name: 'Bowtie2',
          category: 'Read Mapping',
          type: 'APP',
          provider: '系统',
          version: 'v2.4.5',
        },
      ];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  onBeforeMount(() => {
    fetchData();
  });

  // 监听 filters 对象的变化
  watch(
    filters,
    () => {
      currentPage.value = 1; // 重置页码
      fetchData();
    },
    { deep: true }
  );
</script>

<style lang="scss" scoped>
  .header {
    border-bottom: 1px solid #ebeef5;
  }

  .el-select {
    min-width: 100px;
  }

  .el-cascader {
    width: 100%;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status-success {
    background-color: #67c23a;
  }

  .status-error {
    background-color: #f56c6c;
  }

  .status-default {
    background-color: #909399;
  }

  .status-creating {
    background-color: #e6a23c;
  }

  // 新增样式
  .form-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ebeef5;

    &:last-child {
      border-bottom: none;
    }
  }

  .form-section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
  }

  // 添加分页组件样式
  .pagination-container {
    padding: 10px;
    border-radius: 6px;
  }

  .project-list {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .project-detail {
    width: 400px;
    background-color: #fff;
    overflow-y: auto;
    padding: 20px;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    // 添加过渡动画
    transition: transform 0.3s ease;
    transform: translateX(100%);

    &.show {
      transform: translateX(0);
    }

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;
    }

    .detail-content {
      .detail-section {
        margin-bottom: 25px;

        .section-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
          color: #333;
        }

        .info-item {
          display: flex;
          margin-bottom: 12px;
          line-height: 1.5;

          .label {
            width: 80px;
            color: #909399;
          }

          .value {
            flex: 1;
            color: #333;
          }
        }

        .tags-container {
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
  }
</style>
