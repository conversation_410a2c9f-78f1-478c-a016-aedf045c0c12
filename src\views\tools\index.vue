<template>
  <div class="h-full">
    <div v-loading="loading" class="tools-page">
      <div class="header flex items-center justify-between px-5">
        <div class="flex items-center">
          <h2 class="mr-5 text-2xl">工具</h2>
          <el-menu class="relative bottom--1px" mode="horizontal" :ellipsis="false" default-active="1" router>
            <el-menu-item index="1"> 所有工具 </el-menu-item>
          </el-menu>
        </div>

        <!-- 管理员才显示管理入口 -->
        <div v-if="isAdmin">
          <el-button type="primary" @click="goToManage">
            <el-icon><Setting /></el-icon>
            工具管理
          </el-button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="flex gap-4 bg-gray-50 px-5 py-3">
        <div>
          <el-input v-model="filters.name" placeholder="请输入名称" clearable style="width: 200px">
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div>
          <el-select v-model="filters.category" placeholder="所有类别" clearable style="width: 150px">
            <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>

        <div>
          <el-select v-model="filters.type" placeholder="所有类型" clearable style="width: 150px">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>

        <div>
          <el-select v-model="filters.status" placeholder="所有状态" clearable style="width: 150px">
            <el-option label="已上架" value="online" />
            <el-option label="已下架" value="offline" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </div>
      </div>

      <!-- 工具卡片网格 -->
      <div class="h-0 flex flex-1 flex-col px-5 pb-2">
        <div class="tools-grid flex-1 overflow-y-auto">
          <div class="grid grid-cols-1 gap-4 p-4 lg:grid-cols-3 md:grid-cols-2 xl:grid-cols-4">
            <el-card
              v-for="tool in tableData"
              :key="tool.id"
              class="tool-card cursor-pointer transition-shadow hover:shadow-lg"
              shadow="hover"
              @click="handleToolClick(tool)"
            >
              <div class="tool-content">
                <!-- 工具图标和状态 -->
                <div class="tool-header mb-3 flex items-start justify-between">
                  <div class="tool-icon">
                    <el-avatar :size="48" :src="tool.icon" shape="square">
                      <el-icon size="24"><Tools /></el-icon>
                    </el-avatar>
                  </div>
                  <el-tag :type="getStatusType(tool.status)" size="small" class="status-tag">
                    {{ getStatusText(tool.status) }}
                  </el-tag>
                </div>

                <!-- 工具信息 -->
                <div class="tool-info">
                  <h3 class="tool-name mb-1 truncate text-lg font-semibold" :title="tool.name">
                    {{ tool.name }}
                  </h3>
                  <p class="tool-description line-clamp-2 mb-2 text-sm text-gray-600" :title="tool.description">
                    {{ tool.description || '暂无描述' }}
                  </p>

                  <div class="tool-meta text-xs text-gray-500 space-y-1">
                    <div class="flex justify-between">
                      <span>类别: {{ tool.category }}</span>
                      <span>类型: {{ tool.type }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span>提供者: {{ tool.provider }}</span>
                      <span>版本: {{ tool.version }}</span>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="tool-actions mt-3 border-t border-gray-100 pt-3">
                  <el-button
                    type="primary"
                    size="small"
                    class="w-full"
                    :disabled="tool.status !== 'online'"
                    @click.stop="handleRunTool(tool)"
                  >
                    <el-icon><VideoPlay /></el-icon>
                    {{ tool.status === 'online' ? '运行' : '不可用' }}
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && tableData.length === 0" class="empty-state">
            <el-empty description="暂无工具数据">
              <el-button type="primary" @click="fetchData">刷新</el-button>
            </el-empty>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container mt-4 flex justify-center">
          <el-pagination
            background
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48, 96]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, watch, onBeforeMount, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import { Search, Setting, Tools, VideoPlay } from '@element-plus/icons-vue';
  import { useUsers } from '@/store/user-info';

  const router = useRouter();
  const userStore = useUsers();

  // 1. 类型定义和接口
  interface ToolsCustom {
    id: number;
    name: string;
    description?: string;
    category: string;
    type: string;
    provider: string;
    version: string;
    icon?: string;
    status: 'online' | 'offline' | 'maintenance';
    createTime?: string;
    updateTime?: string;
  }

  // 2. 响应式数据
  const loading = ref(false);
  const tableData = ref<ToolsCustom[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(12);
  const total = ref(30);

  const filters = reactive({
    name: '',
    category: '',
    type: '',
    status: '',
  });

  const categoryOptions = [
    { label: '所有类别', value: '' },
    { label: 'Mapping Manipuation', value: 'mapping-manipulation' },
    { label: 'Read Mapping', value: 'read-mapping' },
    { label: 'RNA-Seq', value: 'rna-seq' },
  ];

  const typeOptions = [
    { label: '所有类型', value: '' },
    { label: 'APP', value: 'app' },
  ];

  // 计算属性
  console.log(userStore.user);
  const isAdmin = computed(() => {
    return userStore.user.roleCode.includes('ADMIN');
  });

  // 状态相关方法
  const getStatusType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
    const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
      online: 'success',
      offline: 'danger',
      maintenance: 'warning',
    };
    return typeMap[status] || 'info';
  };

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      online: '已上架',
      offline: '已下架',
      maintenance: '维护中',
    };
    return textMap[status] || status;
  };

  // 事件处理方法
  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    fetchData();
  };

  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchData();
  };

  // 处理工具卡片点击事件
  const handleToolClick = (tool: ToolsCustom) => {
    router.push({
      path: '/tools/tools-detail',
      query: { id: tool.id },
    });
  };

  // 处理运行工具
  const handleRunTool = (tool: ToolsCustom) => {
    if (tool.status !== 'online') {
      ElMessage.warning('该工具当前不可用');
      return;
    }
    ElMessage.success(`正在启动 ${tool.name}...`);
    // 这里可以添加实际的运行逻辑
  };

  // 跳转到管理页面
  const goToManage = () => {
    router.push('/tools/manage');
  };

  async function fetchData() {
    try {
      loading.value = true;
      // const { data } = await dataBrowse();
      await new Promise((resolve) => setTimeout(resolve, 300));
      tableData.value = [
        {
          id: 1,
          name: 'BWA',
          description: '使用-MEM算法将FASTQ读取/配对或未配对映射到参考数据集',
          category: 'Read Mapping',
          type: 'APP',
          provider: '系统',
          version: 'v0.7.17',
          status: 'online' as const,
          icon: '',
          updateTime: '2024-01-15 10:30:00',
        },
        {
          id: 2,
          name: 'STAR',
          description: '高速RNA-seq比对工具，支持剪接比对',
          category: 'RNA-Seq',
          type: 'APP',
          provider: '系统',
          version: 'v2.7.10a',
          status: 'online' as const,
          icon: '',
          updateTime: '2024-01-14 15:20:00',
        },
        {
          id: 3,
          name: 'Samtools',
          description: 'SAM/BAM文件处理工具集',
          category: 'Mapping Manipuation',
          type: 'APP',
          provider: '系统',
          version: 'v1.15',
          status: 'maintenance' as const,
          icon: '',
          updateTime: '2024-01-13 09:15:00',
        },
        {
          id: 4,
          name: 'HISAT2',
          description: '快速敏感的RNA-seq比对工具',
          category: 'RNA-Seq',
          type: 'APP',
          provider: '系统',
          version: 'v2.2.1',
          status: 'online' as const,
          icon: '',
          updateTime: '2024-01-12 14:45:00',
        },
        {
          id: 5,
          name: 'Bowtie2',
          description:
            '快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具快速准确的短序列比对工具',
          category: 'Read Mapping',
          type: 'APP',
          provider: '系统',
          version: 'v2.4.5',
          status: 'offline' as const,
          icon: '',
          updateTime: '2024-01-11 11:30:00',
        },
      ];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  }

  onBeforeMount(() => {
    fetchData();
  });

  // 监听 filters 对象的变化
  watch(
    filters,
    () => {
      currentPage.value = 1; // 重置页码
      fetchData();
    },
    { deep: true }
  );
</script>

<style lang="scss" scoped>
  .tools-page {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .header {
    border-bottom: 1px solid #ebeef5;
  }

  .tools-grid {
    .tool-card {
      height: 280px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .tool-content {
        height: 100%;
        display: flex;
        flex-direction: column;

        .tool-header {
          .status-tag {
            font-size: 11px;
            padding: 2px 6px;
          }
        }

        .tool-info {
          flex: 1;
          padding-bottom: 10px;

          .tool-name {
            color: #303133;
            line-height: 1.4;
          }

          .tool-description {
            line-height: 1.5;
            overflow: hidden;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .tool-meta {
            margin-top: auto;
            padding-top: 8px;
          }
        }

        .tool-actions {
          margin-top: auto;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
  }

  .pagination-container {
    padding: 10px;
    border-radius: 6px;
    background: #fff;
    border-top: 1px solid #ebeef5;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .tools-grid {
      .grid {
        grid-template-columns: 1fr;
      }
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .tools-grid {
      .grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  @media (min-width: 1025px) and (max-width: 1440px) {
    .tools-grid {
      .grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  @media (min-width: 1441px) {
    .tools-grid {
      .grid {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  }
</style>
