import { createRouter, createWebHistory } from 'vue-router';
import { staticRoutes } from './staticRoutes';
import type { App } from 'vue';

export const router = createRouter({
  history: createWebHistory(),
  routes: staticRoutes,
  // 是否应该禁止尾部斜杠。默认为假
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 配置路由器
export function setupRouter(app: App<Element>) {
  app.use(router);
}

//动态路由 asyncRoutes
export const asyncRoutes = [];

export default router;
