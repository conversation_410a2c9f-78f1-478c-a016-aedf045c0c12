/*
 * @OriginalName: 数据资源系统中医学数据集文件管理模块
 * @Description: 医学数据记录信息的创建、更新和查询、文件上传
 */
import { request } from '@/utils/request';

/**
 * 上传Excel中的数据资源清单和医学数据的元数据
 * @description 单独上传数据资源清单和医学数据的元数据，不反馈数据资源清单信息。
 */
export function uploadFile_10(fileInforId: number, data: { mddFile: string }, params?: { fileInforId: number }) {
  return request<RString>(`/FileInfor/uploadFileId/${fileInforId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 导入Excel中的数据资源清单和医学数据的元数据
 * @description 读出数据资源清单。根据数据集记录ID和医学数据的元数据文件，把医学数据的框架结构导入系统中
 */
export function uploadFile_11(
  userIdCur: number,
  cbdDatabaseId: number,
  data: { fileInforDTO: FileInfoDTO },
  params?: { cbdDatabaseId: number; userIdCur: number }
) {
  return request<SseEmitter>(`/FileInfor/uploadFileId/hasmdd/${userIdCur}/${cbdDatabaseId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 设置标签
 * @description 为多个数据集文件设置疾病类型标签，修改为标签由annotationId指定的疾病类别标签。annotationId为0，则清空疾病类型标签。
 */
export function setFileInforAnnotation(annotationId: number, data: Array<number>, params?: { annotationId: number }) {
  return request<RListFileInfoVO>(`/FileInfor/setFileInforAnnotation/${annotationId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_14(data: FileInfoDTO) {
  return request<RFileInfoVO>(`/FileInfor/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 *
 * @description
 */
export function getDataInventory(data: { mddFile: string }) {
  return request<RFileInfoVO>(`/FileInfor/getDataInventory`, {
    method: 'post',
    data,
  });
}

/**
 * 查询数据集数据导入数据库后生成的表
 * @description 按文件ID，查询该数据集文件中的sheet工作表，被导入数据库后生成的数据表CBDDefTable。
 */
export function findTablesByFileInforId(
  fileInforId: number,
  pageNum: number,
  pageSize: number,
  params?: { fileInforId: number; blurName?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPageCBDDefTableCBDDefTableVO>(
    `/FileInfor/findTablesByFileInforId/${fileInforId}/${pageNum}/${pageSize}`,
    {
      method: 'post',
      params,
    }
  );
}

/**
 * 查找数据集
 * @description 按数据集所有者的Id，查询数据集记录。
 */
export function findFileInforByUserId(
  userId: number,
  pageNum: number,
  pageSize: number,
  params?: { userId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPageFileInforFileInfoVO>(`/FileInfor/findFileInforByUserId/${userId}/${pageNum}/${pageSize}`, {
    method: 'post',
    params,
  });
}

/**
 * 查找数据集
 * @description 按动态条件，获取满足相应条件的文档。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findFileInforByCriteria(data: FileInforCriteria) {
  return request<RVOPageFileInforFileInfoVO>(`/FileInfor/findFileInforByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据集
 * @description 查找带有AnnotationId数组中标签的医学数据集。若数组为空，则返回全部记录
 */
export function findFileInforByAnnotationId(data: AnnotationIDListDTO) {
  return request<RVOPageFileInforFileInfoVO>(`/FileInfor/findFileInforByAnnotationId`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_14(data: Array<number>) {
  return request<RListFileInfoVO>(`/FileInfor/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 把多个Excel医学数据集中的医学数据导入数据库
 * @description 根据医学数据集文件命名结构规范：projectCode@<EMAIL>，把医学数据集中的数据导入到数据库中。文件名称必须符合规范。
 */
export function exportMultiMedicalDataSetToDatabase(
  fileId: number,
  data: { datasetFileList: Array<string> },
  params?: { fileId: number }
) {
  return request<SseEmitter>(`/FileInfor/exportMultiMedicalDataSetToDatabase/${fileId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 把Excel医学数据集中的医学数据导入数据库
 * @description 根据医学数据集文件ID和数据库中的数据，把医学数据集中的数据导入到数据库中。
 */
export function exportMedicalDataSetToDatabase(
  fileId: number,
  data: { datasetFile: string },
  params?: { fileId: number }
) {
  return request<SseEmitter>(`/FileInfor/exportMedicalDataSetToDatabase/${fileId}`, {
    method: 'post',
    data,
    params,
  });
}

/**
 * 下载数据集数据原始文件
 * @description 按附件路径名称，下载数据集的原始文件。
 */
export function downloadAttachmentMaterialFile_1(data: string) {
  return request(`/FileInfor/downloadHistoryFile`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_14(data: Array<number>) {
  return request<R>(`/FileInfor/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找线程状态
 * @description 查看导入数据时，启动线程的状态
 */
export function getThreadPoolStatus() {
  return request<RMapStringObject>(`/FileInfor/status`, {
    method: 'get',
  });
}

/**
 * 设置数据集数据存储的数据库
 * @description 按文件ID，设置该数据集文件中的数据所存储的数据库。
 */
export function setDatabase(
  fileInforId: number,
  databaseId: number,
  params?: { fileInforId: number; databaseId: number }
) {
  return request<RCBDDefDatabaseVO>(`/FileInfor/setDatabase/${fileInforId}/${databaseId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询数据集数据导入数据库后生成的医学字段
 * @description 按数据表CBDDefTable的Id，查询该表中的column，被导入后生成的医学字段。
 */
export function findMedicalFieldsByTableId(params?: {
  cbdTableId: number;
  pageNum: number;
  pageSize: number;
  searchInput?: string;
}) {
  return request<RVOPageMedicalFieldMedicalFieldVO>(`/FileInfor/findMedicalFieldsByTableId`, {
    method: 'get',
    params,
  });
}

/**
 * 查询数据集数据导入数据库后生成的医学字段
 * @description 按文件ID，查询该数据集文件中的column列，被导入后生成的医学字段。
 */
export function findMedicalFieldsByFileInforId(
  fileInforId: number,
  pageNum: number,
  pageSize: number,
  params?: { fileInforId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPageMedicalFieldMedicalFieldVO>(
    `/FileInfor/findMedicalFieldsByFileInforId/${fileInforId}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 查询数据集数据导入文件的记录
 * @description 按文件ID，查询该数据集文件历史上所有导入的文件。
 */
export function findHistoryFileByFileInforId(fileInforId: number, params?: { fileInforId: number }) {
  return request<RHistoryFileVO>(`/FileInfor/findHistoryFileByFileInforId/${fileInforId}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询数据集数据导入数据库后生成的字段
 * @description 按文件ID，查询该数据集文件中的column列，被导入数据库后生成的数据列。
 */
export function findFieldsByFileInforId(
  fileInforId: number,
  pageNum: number,
  pageSize: number,
  params?: { fileInforId: number; blurName?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPageCBDDefFieldCBDDefFieldVO>(
    `/FileInfor/findFieldsByFileInforId/${fileInforId}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_47(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListFileInfoVO>(`/FileInfor/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_48(id: number, params?: { id: number }) {
  return request<RFileInfoVO>(`/FileInfor/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_14(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/FileInfor/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_47(idArray: Array<number>, params?: { idArray: Array<number> }) {
  return request<R>(`/FileInfor/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_48(id: number, params?: { id: number }) {
  return request<R>(`/FileInfor/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
